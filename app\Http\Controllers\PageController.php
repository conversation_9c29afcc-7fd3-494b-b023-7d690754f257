<?php

namespace App\Http\Controllers;

use App\Models\Page;

class PageController extends Controller
{
    /**
     * Show the page.
     *
     * @param $url
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function show($id)
    {
        $page = Page::where('slug', $id)
            ->ofLanguage(config('app.locale'))
            ->orderBy('language', 'desc')
            ->firstOrFail();

        return view('pages.show', ['page' => $page]);
    }
}
