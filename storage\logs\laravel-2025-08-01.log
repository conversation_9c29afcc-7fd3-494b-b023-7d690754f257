[2025-08-01 17:12:03] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:12:03] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 28)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}
"} 
[2025-08-01 17:12:04] production.ERROR: Route [dashboard] not defined. {"view":{"view":"D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1465</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","exception":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Symfony\\Component\\HttpKernel\\Exception\\HttpException</span> {<a class=sf-dump-ref>#1466</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
  #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php
96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>646</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Exception`\">previous</span>: <span class=sf-dump-note>BadMethodCallException</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21511 title=\"6 occurrences\">#1511</a><samp data-depth=2 id=sf-dump-*********-ref21511 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
    #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php
93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>86</span>
    <span class=sf-dump-meta>trace</span>: {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php:86</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\routes\\web.php:19
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>routes\\web.php:19</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteFileRegistrar.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:511
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:511</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:465
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:465</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php:194
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteRegistrar.php:194</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:56
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:56</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:40
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:40</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:93</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:81
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:81</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:119
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Support\\ServiceProvider.php:119</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1037
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1037</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1015
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1015</span> { &#8230;5}
      <span class=sf-dump-meta title=\"Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation\\{closure}()
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>{closure}()</span> {}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1014
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1014</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:263
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:263</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:186
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:186</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:170
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:170</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> { &#8230;4}
    </samp>}
  </samp>}
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">statusCode</span>: <span class=sf-dump-num>500</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">headers</span>: []
  <span class=sf-dump-meta>trace</span>: {<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;prepareResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>if (! $this-&gt;isHttpException($e)) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)\">    $e = new HttpException(500, $e-&gt;getMessage(), $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;renderExceptionResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>                ? $this-&gt;prepareJsonResponse($request, $e)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)\">                : $this-&gt;prepareResponse($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $e instanceof ValidationException =&gt; $this-&gt;convertValidationExceptionToResponse($e, $request),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)\">    default =&gt; $this-&gt;renderExceptionResponse($request, $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php:53
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Exceptions\\Handler.php:53</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $exception)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)\">    return parent::render($request, $exception);<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:509
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:509</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;renderException($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)\">    return $this-&gt;app[ExceptionHandler::class]-&gt;render($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:148
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:148</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;handle($request)</span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;renderException($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> {<samp data-depth=3 class=sf-dump-compact>
      &#8250; <code class=\"php\"><span class=sf-dump-default><span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"\">$response = $kernel-&gt;handle(<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Illuminate\\Http\\Request::capture()<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php(2): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\resources\\views\\errors\\500.blade.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\storage\\framework\\views\\00daf7e018dd6bc8ee62d5c2a619dbfe.php(4): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\storage\\framework\\views\\76838e5e247296e0f70817541f77a6f7.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
[2025-08-01 17:12:09] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:12:16] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:12:16] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:12:39] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 28)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}
"} 
[2025-08-01 17:12:39] production.ERROR: Route [dashboard] not defined. {"view":{"view":"D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1465</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","exception":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Symfony\\Component\\HttpKernel\\Exception\\HttpException</span> {<a class=sf-dump-ref>#1466</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
  #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php
96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>646</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Exception`\">previous</span>: <span class=sf-dump-note>BadMethodCallException</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21511 title=\"6 occurrences\">#1511</a><samp data-depth=2 id=sf-dump-**********-ref21511 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
    #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php
93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>86</span>
    <span class=sf-dump-meta>trace</span>: {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php:86</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\routes\\web.php:19
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>routes\\web.php:19</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteFileRegistrar.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:511
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:511</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:465
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:465</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php:194
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteRegistrar.php:194</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:56
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:56</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:40
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:40</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:93</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:81
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:81</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:119
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Support\\ServiceProvider.php:119</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1037
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1037</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1015
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1015</span> { &#8230;5}
      <span class=sf-dump-meta title=\"Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation\\{closure}()
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>{closure}()</span> {}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1014
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1014</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:263
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:263</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:186
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:186</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:170
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:170</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> { &#8230;4}
    </samp>}
  </samp>}
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">statusCode</span>: <span class=sf-dump-num>500</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">headers</span>: []
  <span class=sf-dump-meta>trace</span>: {<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;prepareResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>if (! $this-&gt;isHttpException($e)) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)\">    $e = new HttpException(500, $e-&gt;getMessage(), $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;renderExceptionResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>                ? $this-&gt;prepareJsonResponse($request, $e)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)\">                : $this-&gt;prepareResponse($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $e instanceof ValidationException =&gt; $this-&gt;convertValidationExceptionToResponse($e, $request),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)\">    default =&gt; $this-&gt;renderExceptionResponse($request, $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php:53
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Exceptions\\Handler.php:53</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $exception)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)\">    return parent::render($request, $exception);<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:509
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:509</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;renderException($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)\">    return $this-&gt;app[ExceptionHandler::class]-&gt;render($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:148
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:148</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;handle($request)</span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;renderException($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> {<samp data-depth=3 class=sf-dump-compact>
      &#8250; <code class=\"php\"><span class=sf-dump-default><span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"\">$response = $kernel-&gt;handle(<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Illuminate\\Http\\Request::capture()<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php(2): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\resources\\views\\errors\\500.blade.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\storage\\framework\\views\\00daf7e018dd6bc8ee62d5c2a619dbfe.php(4): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\storage\\framework\\views\\76838e5e247296e0f70817541f77a6f7.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
[2025-08-01 17:12:43] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 28)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#30 {main}
"} 
[2025-08-01 17:12:43] production.ERROR: Route [dashboard] not defined. {"view":{"view":"D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1465</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","exception":"<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>Symfony\\Component\\HttpKernel\\Exception\\HttpException</span> {<a class=sf-dump-ref>#1466</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
  #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php
96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>646</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Exception`\">previous</span>: <span class=sf-dump-note>BadMethodCallException</span> {<a class=sf-dump-ref href=#sf-dump-********-ref21511 title=\"6 occurrences\">#1511</a><samp data-depth=2 id=sf-dump-********-ref21511 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"52 characters\">Method Illuminate\\Routing\\Route::get does not exist.</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
    #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php
93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>86</span>
    <span class=sf-dump-meta>trace</span>: {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Macroable\\Traits\\Macroable.php:86</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\routes\\web.php:19
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>routes\\web.php:19</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteFileRegistrar.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:511
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:511</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:465
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:465</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php:194
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\RouteRegistrar.php:194</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:56
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:56</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php:40
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Providers\\RouteServiceProvider.php:40</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:93</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:122</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:45</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:36</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Util.php:41</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:81
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:81</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\BoundMethod.php:35</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:662
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Container\\Container.php:662</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:119
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Support\\ServiceProvider.php:119</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1037
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1037</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1015
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1015</span> { &#8230;5}
      <span class=sf-dump-meta title=\"Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation\\{closure}()
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Application-&gt;Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>{closure}()</span> {}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:1014
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:1014</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php:17</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php:263
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Application.php:263</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:186
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:186</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:170
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:170</span> { &#8230;4}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span> { &#8230;5}
      <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> { &#8230;4}
    </samp>}
  </samp>}
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">statusCode</span>: <span class=sf-dump-num>500</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">headers</span>: []
  <span class=sf-dump-meta>trace</span>: {<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:646</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;prepareResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>if (! $this-&gt;isHttpException($e)) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareResponse($request, Throwable $e)\">    $e = new HttpException(500, $e-&gt;getMessage(), $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:556</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;renderExceptionResponse($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>                ? $this-&gt;prepareJsonResponse($request, $e)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;renderExceptionResponse($request, Throwable $e)\">                : $this-&gt;prepareResponse($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Exceptions\\Handler.php:473</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $e instanceof ValidationException =&gt; $this-&gt;convertValidationExceptionToResponse($e, $request),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)\">    default =&gt; $this-&gt;renderExceptionResponse($request, $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php:53
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Exceptions\\Handler.php:53</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Handler-&gt;render($request, Throwable $exception)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Exceptions\\Handler-&gt;render($request, Throwable $exception)\">    return parent::render($request, $exception);<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:509
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:509</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;renderException($request, Throwable $e)</span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;renderException($request, Throwable $e)\">    return $this-&gt;app[ExceptionHandler::class]-&gt;render($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:148
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:148</span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;handle($request)</span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;renderException($request, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=sf-dump-meta title=\"D:\\laragon\\www\\phprank\\public\\index.php:74
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\phprank</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:74</span> {<samp data-depth=3 class=sf-dump-compact>
      &#8250; <code class=\"php\"><span class=sf-dump-default><span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"\">$response = $kernel-&gt;handle(<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Illuminate\\Http\\Request::capture()<span class=\"sf-dump-default sf-dump-ns\">\\r</span></span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\resources\\views\\shared\\sidebars\\user.blade.php(2): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\resources\\views\\errors\\500.blade.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard] not defined. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 D:\\laragon\\www\\phprank\\storage\\framework\\views\\00daf7e018dd6bc8ee62d5c2a619dbfe.php(4): route('dashboard')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\phprank\\storage\\framework\\views\\76838e5e247296e0f70817541f77a6f7.php(24): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 500, Array)
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 500, Array)
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#22 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#25 D:\\laragon\\www\\phprank\\app\\Exceptions\\Handler.php(53): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(BadMethodCallException))
#28 D:\\laragon\\www\\phprank\\public\\index.php(74): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
[2025-08-01 17:12:59] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 28)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 D:\\laragon\\www\\phprank\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:13:16] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-08-01 17:13:16] production.ERROR: Method Illuminate\Routing\Route::get does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Routing\\Route::get does not exist. at D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:86)
[stacktrace]
#0 D:\\laragon\\www\\phprank\\routes\\web.php(19): Illuminate\\Routing\\Route::__callStatic('get', Array)
#1 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(56): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\phprank\\app\\Providers\\RouteServiceProvider.php(40): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#7 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#8 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#23 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#24 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 D:\\laragon\\www\\phprank\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
