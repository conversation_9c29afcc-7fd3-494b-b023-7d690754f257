<footer id="footer" class="footer bg-base-0<?php echo e(request()->is('admin/invoices/*') || request()->is('account/invoices/*') ? 'd-print-none' : ''); ?>">
    <div class="container py-5">
        <?php if(!isset($footer['menu']['removed'])): ?>
            <div class="row">
                <div class="col-12 col-lg">
                    <ul class="nav p-0 mx-n3 mb-3 mb-lg-0 d-flex flex-column flex-lg-row">
                        <?php $__currentLoopData = $footer['menu']['links'] ?? [__('Contact') => route('contact'), __('Terms') => config('settings.legal_terms_url'), __('Privacy') => config('settings.legal_privacy_url'), __('Developers') => route('developers')]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $title => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="nav-item">
                                <a href="<?php echo e($url); ?>" class="nav-link py-1"><?php echo e($title); ?></a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php $__currentLoopData = $footerPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="nav-item d-flex">
                                <a href="<?php echo e(route('pages.show', $page['slug'])); ?>" class="nav-link py-1"><?php echo e(__($page['name'])); ?></a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                <div class="col-12 col-lg-auto">
                    <ul class="nav p-0 mx-n2 mb-3 mb-lg-0 d-flex flex-row">
                        <?php $__currentLoopData = $footer['menu']['socials'] ?? [config('settings.social_facebook') => __('Facebook'), config('settings.social_x') => 'X', config('settings.social_instagram') => 'Instagram', config('settings.social_youtube') => 'YouTube', config('settings.social_github') => 'GitHub', config('settings.social_discord') => 'Discord', config('settings.social_reddit') => 'Reddit', config('settings.social_threads') => 'Threads', config('settings.social_tiktok') => 'TikTok', config('settings.social_linkedin') => 'LinkedIn', config('settings.social_tumblr') => 'Tumblr', config('settings.social_pinterest') => 'Pinterest']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url => $title): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($url): ?>
                                <li class="nav-item d-flex">
                                    <a href="<?php echo e($url); ?>" class="nav-link px-2 py-1 text-secondary text-decoration-none d-flex align-items-center" data-tooltip="true" title="<?php echo e($title); ?>" rel="nofollow noreferrer noopener">
                                        <?php echo $__env->make('icons.' . strtolower($title), ['class' => 'fill-current width-5 height-5'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <span class="sr-only"><?php echo e($title); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
            <hr class="<?php echo e((isset($footer['menu']['links']) && empty($footer['menu']['links']) && isset($footer['menu']['socials']) && empty($footer['menu']['socials']) ? 'd-none' : '')); ?>">
        <?php endif; ?>
        <div class="row">
            <div class="col-12 col-lg order-2 order-lg-1">
                <?php if(!isset($footer['copyright']['removed'])): ?>
                    <div class="text-muted py-1"><?php echo e(__('© :year :name.', ['year' => now()->year, 'name' => $footer['copyright']['name'] ?? config('settings.title')])); ?> <?php echo e(__('All rights reserved.')); ?></div>
                <?php endif; ?>
            </div>
            <div class="col-12 col-lg-auto order-1 order-lg-2 d-flex flex-column flex-lg-row">
                <div class="nav p-0 mx-n3 mb-3 mb-lg-0 d-flex flex-column flex-lg-row">
                    <div class="nav-item d-flex">
                        <a href="#" class="nav-link py-1 d-flex align-items-center text-secondary" id="dark-mode" data-tooltip="true" title="<?php echo e(__('Change theme')); ?>">
                            <?php echo $__env->make('icons.contrast', ['class' => 'width-4 height-4 fill-current ' . (__('lang_dir') == 'rtl' ? 'ml-2' : 'mr-2')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <span class="text-muted" data-text-light="<?php echo e(__('Light')); ?>" data-text-dark="<?php echo e(__('Dark')); ?>"><?php echo e((config('settings.dark_mode') == 1 ? __('Dark') : __('Light'))); ?></span>
                        </a>
                    </div>

                    <?php if(count(config('app.locales')) > 1): ?>
                        <div class="nav-item d-flex">
                            <a href="#" class="nav-link py-1 d-flex align-items-center text-secondary" data-toggle="modal" data-target="#change-language-modal" data-tooltip="true" title="<?php echo e(__('Change language')); ?>">
                                <?php echo $__env->make('icons.language', ['class' => 'width-4 height-4 fill-current ' . (__('lang_dir') == 'rtl' ? 'ml-2' : 'mr-2')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <span class="text-muted"><?php echo e(config('app.locales')[config('app.locale')]['name']); ?></span>
                            </a>
                        </div>

                        <div class="modal fade" id="change-language-modal" tabindex="-1" role="dialog" aria-labelledby="change-language-modal-label" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="dialog">
                                <div class="modal-content border-0 shadow">
                                    <div class="modal-header">
                                        <h6 class="modal-title" id="change-language-modal-label"><?php echo e(__('Change language')); ?></h6>
                                        <button type="button" class="close d-flex align-items-center justify-content-center width-12 height-14" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true" class="d-flex align-items-center"><?php echo $__env->make('icons.close', ['class' => 'fill-current width-4 height-4'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                        </button>
                                    </div>
                                    <form action="<?php echo e(route('locale')); ?>" method="post" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <div class="modal-body">
                                            <div class="row">
                                                <?php $__currentLoopData = config('app.locales'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-6">
                                                        <div class="custom-control custom-radio">
                                                            <input type="radio" id="i-language-<?php echo e($code); ?>" name="locale" class="custom-control-input" value="<?php echo e($code); ?>" <?php if(config('app.locale') == $code): ?> checked <?php endif; ?>>
                                                            <label class="custom-control-label" for="i-language-<?php echo e($code); ?>" lang="<?php echo e($code); ?>"><?php echo e($language['name']); ?></label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__('Close')); ?></button>
                                            <button type="submit" class="btn btn-primary"><?php echo e(__('Save')); ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if(!isset($footer['cookie_law']['removed'])): ?>
        <?php echo $__env->make('shared.cookie-law', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
</footer>
<?php /**PATH D:\laragon\www\phprank\resources\views/shared/footer.blade.php ENDPATH**/ ?>