<?php if(auth()->guard()->guest()): ?>
    <div id="header" class="header bg-base-0 position-sticky top-0 right-0 left-0 w-100 box-sizing-border-box z-1025 shadow">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light px-0 py-3">
                <a href="<?php echo e(route('home')); ?>" aria-label="<?php echo e(config('settings.title')); ?>" class="navbar-brand p-0">
                    <div class="height-10 width-auto">
                        <img src="<?php echo e(asset('uploads/brand/' . (config('settings.dark_mode') == 1 ? config('settings.logo_dark') : config('settings.logo')))); ?>" alt="<?php echo e(config('settings.title')); ?>" width="auto" height="40" data-theme-dark="<?php echo e(asset('uploads/brand/' . config('settings.logo_dark'))); ?>" data-theme-light="<?php echo e(asset('uploads/brand/' . config('settings.logo'))); ?>" data-theme-target="src" class="h-100 border-0 max-height-10 object-fit-contain max-width-48">
                    </div>
                </a>
                <button class="navbar-toggler border-0 p-0" type="button" data-toggle="collapse" data-target="#header-navbar" aria-controls="header-navbar" aria-expanded="false" aria-label="<?php echo e(__('Toggle navigation')); ?>">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="header-navbar">
                    <ul class="navbar-nav pt-2 p-lg-0 <?php echo e((__('lang_dir') == 'rtl' ? 'mr-auto' : 'ml-auto')); ?>">
                        <?php if(config('settings.tools_guest')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('tools')); ?>" role="button"><?php echo e(__('Tools')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if(paymentProcessors()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('pricing')); ?>" role="button"><?php echo e(__('Pricing')); ?></a>
                            </li>
                        <?php endif; ?>

                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('login')); ?>" role="button"><?php echo e(__('Login')); ?></a>
                        </li>

                        <?php if(config('settings.registration')): ?>
                            <li class="nav-item d-flex align-items-center">
                                <a class="btn btn-outline-primary" href="<?php echo e(route('register')); ?>" role="button"><?php echo e(__('Register')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        </div>
    </div>
<?php else: ?>
    <div id="header" class="header bg-base-0 position-sticky top-0 right-0 left-0 w-100 box-sizing-border-box z-1025 shadow d-lg-none">
        <div class="container-fluid">
            <nav class="navbar navbar-light px-0 py-3">
                <a href="<?php echo e(route('dashboard')); ?>" aria-label="<?php echo e(config('settings.title')); ?>" class="navbar-brand p-0">
                    <div class="height-10 width-auto">
                        <img src="<?php echo e(asset('uploads/brand/' . (config('settings.dark_mode') == 1 ? config('settings.logo_dark') : config('settings.logo')))); ?>" alt="<?php echo e(config('settings.title')); ?>" width="auto" height="40" data-theme-dark="<?php echo e(asset('uploads/brand/' . config('settings.logo_dark'))); ?>" data-theme-light="<?php echo e(asset('uploads/brand/' . config('settings.logo'))); ?>" data-theme-target="src" class="h-100 border-0 max-height-10 object-fit-contain max-width-48">
                    </div>
                </a>
                <button class="slide-menu-toggle navbar-toggler border-0 p-0" type="button">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </nav>
        </div>
    </div>

    <nav class="slide-menu position-fixed top-0 bottom-0 <?php echo e((__('lang_dir') == 'rtl' ? 'left-auto right-0' : 'left-0')); ?> shadow bg-base-0 navbar navbar-light p-0 d-flex flex-column z-1030" id="slide-menu">
        <div class="min-height-0 flex-grow-1 d-flex flex-column w-100">
            <div>
                <div class="<?php echo e((__('lang_dir') == 'rtl' ? 'pr-4' : 'pl-4')); ?> py-3 d-flex align-items-center">
                    <a href="<?php echo e(route('dashboard')); ?>" aria-label="<?php echo e(config('settings.title')); ?>" class="navbar-brand m-0 p-0">
                        <div class="height-10 width-auto">
                            <img src="<?php echo e(asset('uploads/brand/' . (config('settings.dark_mode') == 1 ? config('settings.logo_dark') : config('settings.logo')))); ?>" alt="<?php echo e(config('settings.title')); ?>" width="auto" height="40" data-theme-dark="<?php echo e(asset('uploads/brand/' . config('settings.logo_dark'))); ?>" data-theme-light="<?php echo e(asset('uploads/brand/' . config('settings.logo'))); ?>" data-theme-target="src" class="h-100 border-0 max-height-10 object-fit-contain max-width-48">
                        </div>
                    </a>
                    <div class="close slide-menu-toggle cursor-pointer d-lg-none d-flex align-items-center <?php echo e((__('lang_dir') == 'rtl' ? 'mr-auto' : 'ml-auto')); ?> px-4 py-2">
                        <?php echo $__env->make('icons.close', ['class' => 'fill-current width-4 height-4'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>

            <div class="d-flex align-items-center">
                <div class="py-3 <?php echo e((__('lang_dir') == 'rtl' ? 'pr-4 pl-0' : 'pl-4 pr-0')); ?> font-weight-medium text-muted text-uppercase flex-grow-1"><?php echo e(__('Menu')); ?></div>

                <?php if(Auth::user()->role == 1): ?>
                    <?php if(request()->is('admin/*')): ?>
                        <a class="px-4 py-2 text-decoration-none text-secondary" href="<?php echo e(route('dashboard')); ?>" data-tooltip="true" title="<?php echo e(__('User')); ?>" role="button"><span class="d-flex align-items-center"><?php echo $__env->make('icons.account-circle', ['class' => 'width-4 height-4 fill-current'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span></a>
                    <?php else: ?>
                        <a class="px-4 py-2 text-decoration-none text-secondary" href="<?php echo e(route('admin.dashboard')); ?>" data-tooltip="true" title="<?php echo e(__('Admin')); ?>" role="button"><span class="d-flex align-items-center"><?php echo $__env->make('icons.supervised-user-circle', ['class' => 'width-4 height-4 fill-current'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span></a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <div class="min-height-0 flex-grow-1 overflow-auto sidebar">
                <?php echo $__env->yieldContent('menu'); ?>
            </div>

            <a href="<?php echo e(route('account.plan')); ?>" class="text-decoration-none py-2 px-2 my-2 mx-3">
                <div class="row no-gutters">
                    <div class="col">
                        <div class="small text-muted">
                            <?php echo e(__(':number of :total reports used.', ['number' => shortenNumber($reportsCount), 'total' => (Auth::user()->plan->features->reports < 0 ? '∞' : shortenNumber(Auth::user()->plan->features->reports))])); ?>

                        </div>
                    </div>
                </div>

                <div class="progress w-100 my-2 height-1.25">
                    <div class="progress-bar rounded" role="progressbar" style="width: <?php echo e((Auth::user()->plan->features->reports == 0 ? 100 : (($reportsCount / Auth::user()->plan->features->reports) * 100))); ?>%"></div>
                </div>
            </a>

            <div class="sidebar sidebar-footer">
                <div class="py-3 <?php echo e((__('lang_dir') == 'rtl' ? 'pr-4 pl-0' : 'pl-4 pr-0')); ?> d-flex align-items-center" aria-expanded="true">
                    <a href="<?php echo e(route('account')); ?>" class="d-flex align-items-center overflow-hidden text-secondary text-decoration-none flex-grow-1">
                        <img src="<?php echo e(Auth::user()->avatarUrl); ?>" class="flex-shrink-0 rounded-circle width-10 height-10 <?php echo e((__('lang_dir') == 'rtl' ? 'ml-3' : 'mr-3')); ?>">

                        <div class="d-flex flex-column text-truncate">
                            <div class="font-weight-medium text-dark text-truncate">
                                <?php echo e(Auth::user()->name); ?>

                            </div>

                            <div class="small font-weight-medium">
                                <?php echo e(__('Account')); ?>

                            </div>
                        </div>
                    </a>

                    <a class="py-2 px-4 d-flex flex-shrink-0 align-items-center text-secondary" href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" data-tooltip="true" title="<?php echo e(__('Logout')); ?>"><?php echo $__env->make('icons.logout', ['class' => 'fill-current width-4 height-4'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></a>

                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                        <?php echo csrf_field(); ?>
                    </form>
                </div>
            </div>
        </div>
    </nav>
<?php endif; ?>
<?php /**PATH D:\laragon\www\phprank\resources\views/shared/header.blade.php ENDPATH**/ ?>