<?php $__env->startSection('menu'); ?>
    <?php
        $menu = [
            route('dashboard') => [
                'icon' => 'grid-view',
                'title' => __('Dashboard')
            ],
            route('reports') => [
                'icon' => 'list-alt',
                'title' => __('Reports')
            ],
            route('projects') => [
                'icon' => 'account-tree',
                'title' => __('Projects')
            ],
            route('tools') => [
                'icon' => 'handyman',
                'title' => __('Tools')
            ]
        ];
    ?>

    <div class="nav d-block text-truncate">
        <?php $__currentLoopData = $menu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link d-flex px-4 <?php if(str_starts_with(request()->url(), $key) && !isset($value['menu'])): ?> active <?php endif; ?>" <?php if(isset($value['menu'])): ?> data-toggle="collapse" href="#sub-menu-<?php echo e($key); ?>" role="button" <?php if(array_filter(array_keys($value['menu']), function ($url) { return str_starts_with(request()->url(), $url); })): ?> aria-expanded="true" <?php else: ?> aria-expanded="false" <?php endif; ?> aria-controls="collapse-<?php echo e($key); ?>" <?php else: ?> href="<?php echo e($key); ?>" <?php endif; ?>>
                    <span class="sidebar-icon d-flex align-items-center"><?php echo $__env->make('icons.' . $value['icon'], ['class' => 'fill-current width-4 height-4 '.(__('lang_dir') == 'rtl' ? 'ml-3' : 'mr-3')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                    <span class="flex-grow-1 text-truncate"><?php echo e($value['title']); ?></span>
                    <?php if(isset($value['menu'])): ?> <span class="d-flex align-items-center ml-auto sidebar-expand"><?php echo $__env->make('icons.expand-more', ['class' => 'fill-current text-muted width-3 height-3'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span> <?php endif; ?>
                </a>
            </li>

            <?php if(isset($value['menu'])): ?>
                <div class="collapse sub-menu <?php if(array_filter(array_keys($menu[$key]['menu']), function ($url) { return str_starts_with(request()->url(), $url); })): ?> show <?php endif; ?>" id="sub-menu-<?php echo e($key); ?>">
                    <?php $__currentLoopData = $value['menu']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subKey => $subValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($subKey); ?>" class="nav-link px-4 d-flex text-truncate <?php if(str_starts_with(request()->url(), $subKey)): ?> active <?php endif; ?>">
                            <span class="sidebar-icon d-flex align-items-center"><?php echo $__env->make('icons.' . $subValue['icon'], ['class' => 'fill-current width-4 height-4 '.(__('lang_dir') == 'rtl' ? 'ml-3' : 'mr-3')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                            <span class="flex-grow-1 text-truncate"><?php echo e($subValue['title']); ?></span>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php $__env->stopSection(); ?>
<?php /**PATH D:\laragon\www\phprank\resources\views/shared/sidebars/user.blade.php ENDPATH**/ ?>