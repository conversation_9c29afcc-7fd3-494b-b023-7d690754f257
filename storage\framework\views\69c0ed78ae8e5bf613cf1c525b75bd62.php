

<?php $__env->startSection('body'); ?>
    <body class="d-flex flex-column">
        <?php if(auth()->guard()->guest()): ?>
            <?php if(config('settings.announcement_guest')): ?>
                <?php echo $__env->make('shared.announcement', ['message' => config('settings.announcement_guest'), 'type' => config('settings.announcement_guest_type'), 'id' => config('settings.announcement_guest_id')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php else: ?>
            <?php if(config('settings.announcement_user')): ?>
                <?php echo $__env->make('shared.announcement', ['message' => config('settings.announcement_user'), 'type' => config('settings.announcement_user_type'), 'id' => config('settings.announcement_user_id')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php echo $__env->make('shared.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="d-flex flex-column flex-fill <?php if(auth()->guard()->check()): ?> content <?php endif; ?>">
            <?php echo $__env->yieldContent('content'); ?>

            <?php echo $__env->make('shared.footer', ['footer' => ['menu' => ['removed' => true]]], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </body>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.wrapper', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\phprank\resources\views/layouts/error.blade.php ENDPATH**/ ?>